"""Copyright: <PERSON><PERSON><PERSON> (2024)."""
import math
import time
from typing import Dict, List, Optional, Union

import datasets
import torch
from torch.utils.data import DataLoader, Dataset, BatchSampler, ConcatDataset, RandomSampler
from transformers import Trainer, TrainerCallback, TrainingArguments, TrainerState, TrainerControl, \
    is_torch_xla_available, is_datasets_available
from transformers.debug_utils import DebugOption
from transformers.modeling_utils import unwrap_model
from transformers.trainer_utils import speed_metrics
from transformers.trainer_utils import SaveStrategy
from transformers.utils import logging

######### code for extra loss #########
logger = logging.get_logger(__name__)

if is_torch_xla_available():
    import torch_xla.core.xla_model as xm
    import torch_xla.debug.metrics as met

class AddExtraLossesToTrainerState(TrainerCallback):
    def __init__(self, extra_losses: List[str]):
        self.extra_losses = extra_losses

    def on_train_begin(self, args: TrainingArguments, state: TrainerState, control: TrainerControl, **kwargs):
        control.extra_losses = {k: torch.tensor(0.0).to(args.device) for k in self.extra_losses}
        return control

######### code for custom batch #########

from typing import Any, NamedTuple, Optional, Union, Callable
from functools import partial
from torch.utils.data import DataLoader
from transformers.trainer_utils import seed_worker

class GroupBatchSampler(BatchSampler):
    def __init__(self, dataset: ConcatDataset, batch_size: int, drop_last: bool, language_ds_startidx: int = 2):
        contra_sampler = RandomSampler(ConcatDataset([dataset.datasets[0:language_ds_startidx]]))
        self.language_sampler = RandomSampler(
            ConcatDataset(dataset.datasets[language_ds_startidx:]) if len(dataset.datasets) > language_ds_startidx else dataset.datasets[-1]
        )
        super().__init__(contra_sampler, batch_size, drop_last)
        lengths = torch.tensor([len(g) for g in dataset.datasets])
        self.ratio = 1 - (lengths[language_ds_startidx:].sum() / lengths.sum()).item()
        self.group_bsz = int(self.ratio * self.batch_size) + 1
        self.language_bsz = self.batch_size - self.group_bsz
        self.language_dataset_offset = lengths[:language_ds_startidx].sum().item()

    def dam_generator(self):
        batch = []
        for idx in self.language_sampler:
            batch.append(idx + self.language_dataset_offset)
            if len(batch) == self.language_bsz:
                yield batch
                batch = []

    def __iter__(self):
        batch = []
        dam_generator = self.dam_generator()
        for idx in self.sampler:
            batch.append(idx)
            if len(batch) == self.group_bsz:
                batch.extend(next(dam_generator))
                yield batch
                batch = []

######### trainer #########

class CustomTrainer(Trainer):
    def __init__(self, extra_losses: List[str] = None, language_ds_startidx: int = 2, **kwargs):
        super().__init__(**kwargs)
        self.language_ds_startidx = language_ds_startidx
        if extra_losses is not None:
            self.add_callback(AddExtraLossesToTrainerState(extra_losses))

        self.eval_dataloader = None

    def _get_train_batch_sampler(self, dataset: ConcatDataset, batch_size: int, drop_last: bool) -> BatchSampler:
        return GroupBatchSampler(dataset, batch_size=batch_size, drop_last=drop_last, language_ds_startidx=self.language_ds_startidx)

    def get_train_dataloader(self) -> DataLoader:
        if self.train_dataset is None:
            raise ValueError("Trainer: training requires a train_dataset.")

        train_dataset = self.train_dataset
        data_collator = self.data_collator
        if is_datasets_available() and isinstance(train_dataset, datasets.Dataset):
            train_dataset = self._remove_unused_columns(train_dataset, description="training")
        else:
            data_collator = self._get_collator_with_removed_columns(data_collator, description="training")

        dataloader_params = {
            # "batch_size": self._train_batch_size,
            "collate_fn": data_collator,
            "num_workers": self.args.dataloader_num_workers,
            "pin_memory": self.args.dataloader_pin_memory,
            "persistent_workers": self.args.dataloader_persistent_workers,
            "batch_sampler": self._get_train_batch_sampler(train_dataset, self.args.train_batch_size, self.args.dataloader_drop_last),
        }

        if not isinstance(train_dataset, torch.utils.data.IterableDataset):
            # dataloader_params["sampler"] = self._get_train_sampler()
            # dataloader_params["drop_last"] = self.args.dataloader_drop_last
            dataloader_params["worker_init_fn"] = seed_worker
            dataloader_params["prefetch_factor"] = self.args.dataloader_prefetch_factor

        return self.accelerator.prepare(DataLoader(train_dataset, **dataloader_params))

    def compute_loss(self, model, inputs, return_outputs=False, num_items_in_batch=None):
        if hasattr(self.control, 'extra_losses') and model.training:
            loss, outputs = super().compute_loss(model, inputs, return_outputs=True)

            if not isinstance(outputs, dict):
                raise ValueError("The model output should be a dictionary or ModelOutput and not a tuple or list.")
            for k, v in outputs.items():
                if k in self.control.extra_losses:
                    if v is not None:
                        if self.args.n_gpu > 1:
                            v = v.mean()
                        self.control.extra_losses[k] += v.detach() / self.args.gradient_accumulation_steps

            return (loss, outputs) if return_outputs else loss
        else:
            return super().compute_loss(model, inputs, return_outputs=return_outputs)

    def _maybe_log_save_evaluate(self, tr_loss, grad_norm, model, trial, epoch, ignore_keys_for_eval, start_time, learning_rate=None):
        if self.control.should_log and self.state.global_step > self._globalstep_last_logged:
            if is_torch_xla_available():
                xm.mark_step()

            logs: Dict[str, float] = {}

            # all_gather + mean() to get average loss over all processes
            tr_loss_scalar = self._nested_gather(tr_loss).mean().item()

            # reset tr_loss to zero
            tr_loss -= tr_loss

            logs["loss"] = round(tr_loss_scalar / (self.state.global_step - self._globalstep_last_logged), 4)
            if grad_norm is not None:
                logs["grad_norm"] = grad_norm.item() if isinstance(grad_norm, torch.Tensor) else grad_norm
            if learning_rate is not None:
                logs["learning_rate"] = learning_rate
            else:
                logs["learning_rate"] = self._get_learning_rate()

            if hasattr(self.control, 'extra_losses'):
                for k, v in self.control.extra_losses.items():
                    logs[k] = self._nested_gather(v).mean().item()
                    # reset the loss
                    self.control.extra_losses[k] -= self.control.extra_losses[k]

                    logs[k] = round(logs[k] / (self.state.global_step - self._globalstep_last_logged), 4)

            logs.update(unwrap_model(model).get_extra_logging_dict()
                        if hasattr(unwrap_model(model), 'get_extra_logging_dict') else {})

            self._total_loss_scalar += tr_loss_scalar
            self._globalstep_last_logged = self.state.global_step
            self.store_flos()

            self.log(logs, start_time)

        metrics = None
        if self.control.should_evaluate:
            metrics = self._evaluate(trial, ignore_keys_for_eval)
            is_new_best_metric = self._determine_best_metric(metrics=metrics, trial=trial)

            if self.args.save_strategy == SaveStrategy.BEST:
                self.control.should_save = is_new_best_metric

        if self.control.should_save:
            self._save_checkpoint(model, trial)
            self.control = self.callback_handler.on_save(self.args, self.state, self.control)

    def _create_eval_dataloader(self, eval_dataset):
        logger.warning("Eval dataloader is being created (again)."
                       "Ignore if this is the first time you are seeing this message."
                       "Otherwise, check the CustomTrainer implementation.")
        data_collator = self.data_collator

        if is_datasets_available() and isinstance(eval_dataset, datasets.Dataset):
            eval_dataset = self._remove_unused_columns(eval_dataset, description="evaluation")
        else:
            data_collator = self._get_collator_with_removed_columns(data_collator, description="evaluation")

        dataloader_params = {
            "batch_size": self.args.eval_batch_size,
            "collate_fn": data_collator,
            "num_workers": self.args.dataloader_num_workers,
            "pin_memory": self.args.dataloader_pin_memory,
            "persistent_workers": self.args.dataloader_persistent_workers,
        }

        if not isinstance(eval_dataset, torch.utils.data.IterableDataset):
            dataloader_params["sampler"] = self._get_eval_sampler(eval_dataset)
            dataloader_params["drop_last"] = self.args.dataloader_drop_last
            dataloader_params["prefetch_factor"] = self.args.dataloader_prefetch_factor

        return self.accelerator.prepare(DataLoader(eval_dataset, **dataloader_params))

    def get_eval_dataloader(self,
                            eval_dataset: Optional[Dataset] = None,
                            eval_dataset_name: Optional[str] = None) -> DataLoader:
        """
        Returns the evaluation [`~torch.utils.data.DataLoader`].

        Subclass and override this method if you want to inject some custom behavior.

        Args:
            eval_dataset (`torch.utils.data.Dataset`, *optional*):
                WARNING: Not used here, only for signature compatibility.
                If provided, will override `self.eval_dataset`. If it is a [`~datasets.Dataset`], columns not accepted
                by the `model.forward()` method are automatically removed. It must implement `__len__`.
            eval_dataset_name (`str`, *optional*):

        """
        if eval_dataset is not None:
            raise ValueError("In this custom trainer, you need to pass the name of dataset")

        if eval_dataset_name is None and self.eval_dataset is None:
            raise ValueError("Trainer: evaluation requires an eval_dataset.")
        if self.eval_dataloader is None:
            # create all eval dataloaders if they don't exist
            if isinstance(self.eval_dataset, dict):
                self.eval_dataloader = {}
                for _eval_dataset_name, _eval_dataset in self.eval_dataset.items():
                    self.eval_dataloader[_eval_dataset_name] = self._create_eval_dataloader(_eval_dataset)
            else:
                self.eval_dataloader = self._create_eval_dataloader(self.eval_dataset)

        if isinstance(self.eval_dataloader, dict):
            return self.eval_dataloader[eval_dataset_name]
        else:
            return self.eval_dataloader

    def evaluate(
        self,
        eval_dataset: Optional[Union[Dataset, str, Dict[str, Dataset]]] = None,
        ignore_keys: Optional[List[str]] = None,
        metric_key_prefix: str = "eval",
    ) -> Dict[str, float]:
        """
        Run evaluation and returns metrics.

        The calling script will be responsible for providing a method to compute metrics, as they are task-dependent
        (pass it to the init `compute_metrics` argument).

        You can also subclass and override this method to inject custom behavior.

        Args:
            eval_dataset (Union[`str`, Dict[str, `Dataset`]), *optional*):
                Pass a dataset if you wish to override `self.eval_dataset`. If it is a [`~datasets.Dataset`], columns
                not accepted by the `model.forward()` method are automatically removed. If it is a dictionary, it will
                evaluate on each dataset, prepending the dictionary key to the metric name. Datasets must implement the
                `__len__` method.

                <Tip>

                If you pass a dictionary with names of datasets as keys and datasets as values, evaluate will run
                separate evaluations on each dataset. This can be useful to monitor how training affects other
                datasets or simply to get a more fine-grained evaluation.
                When used with `load_best_model_at_end`, make sure `metric_for_best_model` references exactly one
                of the datasets. If you, for example, pass in `{"data1": data1, "data2": data2}` for two datasets
                `data1` and `data2`, you could specify `metric_for_best_model="eval_data1_loss"` for using the
                loss on `data1` and `metric_for_best_model="eval_data1_loss"` for the loss on `data2`.

                </Tip>

            ignore_keys (`List[str]`, *optional*):
                A list of keys in the output of your model (if it is a dictionary) that should be ignored when
                gathering predictions.
            metric_key_prefix (`str`, *optional*, defaults to `"eval"`):
                An optional prefix to be used as the metrics key prefix. For example the metrics "bleu" will be named
                "eval_bleu" if the prefix is "eval" (default)

        Returns:
            A dictionary containing the evaluation loss and the potential metrics computed from the predictions. The
            dictionary also contains the epoch number which comes from the training state.
        """
        # handle multipe eval datasets
        override = eval_dataset is not None
        eval_dataset = eval_dataset if override else self.eval_dataset
        if isinstance(eval_dataset, dict):
            metrics = {}
            for eval_dataset_name, _eval_dataset in eval_dataset.items():
                dataset_metrics = self.evaluate(
                    eval_dataset=eval_dataset_name if override else _eval_dataset,
                    ignore_keys=ignore_keys,
                    metric_key_prefix=f"{metric_key_prefix}_{eval_dataset_name}",
                )
                metrics.update(dataset_metrics)
            return metrics

        # memory metrics - must set up as early as possible
        self._memory_tracker.start()

        eval_dataloader = self.get_eval_dataloader(eval_dataset_name=eval_dataset)
        start_time = time.time()

        eval_loop = self.prediction_loop if self.args.use_legacy_prediction_loop else self.evaluation_loop
        output = eval_loop(
            eval_dataloader,
            description="Evaluation",
            # No point gathering the predictions if there are no metrics, otherwise we defer to
            # self.args.prediction_loss_only
            prediction_loss_only=True if self.compute_metrics is None else None,
            ignore_keys=ignore_keys,
            metric_key_prefix=metric_key_prefix,
        )

        total_batch_size = self.args.eval_batch_size * self.args.world_size
        if f"{metric_key_prefix}_jit_compilation_time" in output.metrics:
            start_time += output.metrics[f"{metric_key_prefix}_jit_compilation_time"]
        if f"{metric_key_prefix}_model_preparation_time" in output.metrics:
            start_time += output.metrics[f"{metric_key_prefix}_model_preparation_time"]
        output.metrics.update(
            speed_metrics(
                metric_key_prefix,
                start_time,
                num_samples=output.num_samples,
                num_steps=math.ceil(output.num_samples / total_batch_size),
            )
        )

        self.log(output.metrics)

        if DebugOption.TPU_METRICS_DEBUG in self.args.debug:
            # tpu-comment: Logging debug metrics for PyTorch/XLA (compile, execute times, ops, etc.)
            xm.master_print(met.metrics_report())

        self.control = self.callback_handler.on_evaluate(self.args, self.state, self.control, output.metrics)

        self._memory_tracker.stop_and_update_metrics(output.metrics)

        return output.metrics
from typing import Tuple, Optional, List, Union
import torch
from transformers.utils import logging

logger = logging.get_logger(__name__)

from transformers import Qwen2_5_VLForConditionalGeneration
from torch import nn
import torch.distributed as dist
from transformers.modeling_outputs import SequenceClassifierOutput
import torch.nn.functional as F

class Similarity(nn.Module):
    """
    Dot product or cosine similarity
    """

    def __init__(self, temp=0.07):
        super().__init__()
        self.temp = temp
        self.cos = nn.CosineSimilarity(dim=-1)

    def forward(self, x, y):
        return self.cos(x, y) / self.temp

from dataclasses import dataclass
@dataclass
class ExtraLossOutput(SequenceClassifierOutput):
    loss_emb: torch.FloatTensor = None
    loss_gen: torch.FloatTensor = None

class Qwen2_5_VLRetForConditionalGeneration(Qwen2_5_VLForConditionalGeneration):

    def __init__(self, config):
        super().__init__(config)
        self.emb_head = nn.Linear(config.hidden_size, config.hidden_size, bias=True)
        self._init_embhead_weights()

    def _init_embhead_weights(self):
        nn.init.constant_(self.emb_head.weight, 0)
        nn.init.constant_(self.emb_head.bias, 0)

    def forward(
        self,
        input_ids: torch.LongTensor = None,
        attention_mask: Optional[torch.Tensor] = None,
        position_ids: Optional[torch.LongTensor] = None,
        past_key_values: Optional[List[torch.FloatTensor]] = None,
        inputs_embeds: Optional[torch.FloatTensor] = None,
        labels: Optional[torch.LongTensor] = None,
        use_cache: Optional[bool] = None,
        output_attentions: Optional[bool] = None,
        output_hidden_states: Optional[bool] = None,
        return_dict: Optional[bool] = None,
        pixel_values: Optional[torch.Tensor] = None,
        pixel_values_videos: Optional[torch.FloatTensor] = None,
        image_grid_thw: Optional[torch.LongTensor] = None,
        video_grid_thw: Optional[torch.LongTensor] = None,
        rope_deltas: Optional[torch.LongTensor] = None,
        cache_position: Optional[torch.LongTensor] = None,
        second_per_grid_ts: Optional[torch.Tensor] = None,
        inference=False,
        has_hard_negative=False,
        qids=None,
        dids=None,
        ids=None
    ):
        output_attentions = output_attentions if output_attentions is not None else self.config.output_attentions
        output_hidden_states = (
            output_hidden_states if output_hidden_states is not None else self.config.output_hidden_states
        )
        return_dict = return_dict if return_dict is not None else self.config.use_return_dict

        if inputs_embeds is None:
            inputs_embeds = self.model.embed_tokens(input_ids)
            if pixel_values is not None:
                pixel_values = pixel_values.type(self.visual.dtype)
                image_embeds = self.visual(pixel_values, grid_thw=image_grid_thw)
                n_image_tokens = (input_ids == self.config.image_token_id).sum().item()
                n_image_features = image_embeds.shape[0]
                if n_image_tokens != n_image_features:
                    raise ValueError(
                        f"Image features and image tokens do not match: tokens: {n_image_tokens}, features {n_image_features}"
                    )

                mask = input_ids == self.config.image_token_id
                mask_unsqueezed = mask.unsqueeze(-1)
                mask_expanded = mask_unsqueezed.expand_as(inputs_embeds)
                image_mask = mask_expanded.to(inputs_embeds.device)

                image_embeds = image_embeds.to(inputs_embeds.device, inputs_embeds.dtype)
                inputs_embeds = inputs_embeds.masked_scatter(image_mask, image_embeds)

            if pixel_values_videos is not None:
                pixel_values_videos = pixel_values_videos.type(self.visual.dtype)
                video_embeds = self.visual(pixel_values_videos, grid_thw=video_grid_thw)
                n_video_tokens = (input_ids == self.config.video_token_id).sum().item()
                n_video_features = video_embeds.shape[0]
                if n_video_tokens != n_video_features:
                    raise ValueError(
                        f"Video features and video tokens do not match: tokens: {n_video_tokens}, features {n_video_features}"
                    )

                mask = input_ids == self.config.video_token_id
                mask_unsqueezed = mask.unsqueeze(-1)
                mask_expanded = mask_unsqueezed.expand_as(inputs_embeds)
                video_mask = mask_expanded.to(inputs_embeds.device)

                video_embeds = video_embeds.to(inputs_embeds.device, inputs_embeds.dtype)
                inputs_embeds = inputs_embeds.masked_scatter(video_mask, video_embeds)

            if attention_mask is not None:
                attention_mask = attention_mask.to(inputs_embeds.device)

        # if we get 4D attention mask we cannot calculate rope deltas anymore. TODO @raushan fixme
        if position_ids is None and (attention_mask is None or attention_mask.ndim == 2):
            # calculate RoPE index once per generation in the pre-fill stage only
            if (
                (cache_position is not None and cache_position[0] == 0)
                or self.rope_deltas is None
                or (past_key_values is None or past_key_values.get_seq_length() == 0)
            ):
                position_ids, rope_deltas = self.get_rope_index(
                    input_ids,
                    image_grid_thw,
                    video_grid_thw,
                    second_per_grid_ts,
                    attention_mask,
                )
                self.rope_deltas = rope_deltas
            # then use the prev pre-calculated rope-deltas to get the correct position ids
            else:
                batch_size, seq_length, _ = inputs_embeds.shape
                delta = (
                    (cache_position[0] + self.rope_deltas).to(inputs_embeds.device)
                    if cache_position is not None
                    else 0
                )
                position_ids = torch.arange(seq_length, device=inputs_embeds.device)
                position_ids = position_ids.view(1, -1).expand(batch_size, -1)
                if cache_position is not None:  # otherwise `deltas` is an int `0`
                    delta = delta.repeat_interleave(batch_size // delta.shape[0], dim=0)
                position_ids = position_ids.add(delta)
                position_ids = position_ids.unsqueeze(0).expand(3, -1, -1)

        outputs = self.model(
            input_ids=None,
            position_ids=position_ids,
            attention_mask=attention_mask,
            past_key_values=past_key_values,
            inputs_embeds=inputs_embeds,
            use_cache=use_cache,
            output_attentions=output_attentions,
            output_hidden_states=output_hidden_states,
            return_dict=return_dict,
            cache_position=cache_position,
        )

        hidden_states = outputs[0]

        embed_index = self.config.emb_token_ids[0]
        
        # language generation
        language_loss = None
        language_indices = torch.where((labels != embed_index).all(1))[0]
        if language_indices is not None and len(language_indices) > 0:
            logits = self.lm_head(hidden_states[language_indices])
            if labels is not None:
                # Upcast to float if we need to compute the loss to avoid potential precision issues
                logits = logits.float()
                # Shift so that tokens < n predict n
                shift_logits = logits[..., :-1, :].contiguous()
                shift_labels = labels[language_indices][..., 1:].contiguous()
                # Flatten the tokens
                loss_fct = nn.CrossEntropyLoss()
                shift_logits = shift_logits.view(-1, self.config.vocab_size)
                shift_labels = shift_labels.view(-1)
                # Enable model parallelism
                shift_labels = shift_labels.to(shift_logits.device)
                language_loss = loss_fct(shift_logits, shift_labels)

        # contrastive learning
        contrastive_loss = None
        contrastive_indices = torch.where(labels == embed_index)[0]
        if len(contrastive_indices) > 0:
            contrastive_hidden_states = self.emb_head(hidden_states[contrastive_indices])
            contrastive_labels = labels[contrastive_indices] if labels is not None else None

        if dist.is_initialized() and not inference:
            # 收集所有rank的contrastive_indices数量
            local_count = torch.tensor([len(contrastive_indices)], device=contrastive_indices.device)
            count_list = [torch.zeros_like(local_count) for _ in range(dist.get_world_size())]
            dist.all_gather(count_list, local_count)
            
            max_count = max([c.item() for c in count_list])
            
            # 如果当前rank的数量少于最大值，需要padding
            if len(contrastive_indices) < max_count:
                padding_size = max_count - len(contrastive_indices)
                # 对hidden_states进行padding，需要匹配完整的形状
                padding_shape = (padding_size,) + contrastive_hidden_states.shape[1:]  # 保持除第一维外的所有维度
                padding_hidden = torch.zeros(padding_shape, 
                                            device=contrastive_hidden_states.device, 
                                            dtype=contrastive_hidden_states.dtype)
                contrastive_hidden_states = torch.cat([contrastive_hidden_states, padding_hidden], dim=0)
                
                # 对labels进行padding（如果存在）
                if contrastive_labels is not None:
                    padding_labels_shape = (padding_size,) + contrastive_labels.shape[1:]
                    padding_labels = torch.zeros(padding_labels_shape, 
                                                device=contrastive_labels.device, 
                                                dtype=contrastive_labels.dtype)
            contrastive_labels = torch.cat([contrastive_labels, padding_labels], dim=0)
            # 验证拼接后的形状一致性
            assert contrastive_hidden_states.shape[0] == contrastive_labels.shape[0], \
                f"Shape mismatch: hidden states {contrastive_hidden_states.shape[0]} vs labels {contrastive_labels.shape[0]}"
            
            # 现在所有rank的tensor都有相同尺寸，可以进行all_gather
            hidden_list = [torch.zeros_like(contrastive_hidden_states) for _ in range(dist.get_world_size())]
            dist.all_gather(hidden_list, contrastive_hidden_states)
            
            if contrastive_labels is not None:
                labels_list = [torch.zeros_like(contrastive_labels) for _ in range(dist.get_world_size())]
                dist.all_gather(labels_list, contrastive_labels)
            
            # 合并所有rank的数据，但只保留有效部分
            all_hidden_states = []
            all_labels = []
            for i, (hidden, count) in enumerate(zip(hidden_list, count_list)):
                valid_count = count.item()
                if valid_count > 0:
                    all_hidden_states.append(hidden[:valid_count])
                    if contrastive_labels is not None:
                        all_labels.append(labels_list[i][:valid_count])
            
            if all_hidden_states:
                contrastive_hidden_states = torch.cat(all_hidden_states, dim=0)
                if all_labels:
                    contrastive_labels = torch.cat(all_labels, dim=0)

        if has_hard_negative:
            contrastive_batch_size = len(contrastive_hidden_states) // 3
        elif not inference:
            contrastive_batch_size = len(contrastive_hidden_states) // 2
        elif inference:
            contrastive_batch_size = len(contrastive_hidden_states)

        if contrastive_labels is not None:
            embed_indices = torch.argmax((contrastive_labels == embed_index).int(), dim=1)
            embed_features = contrastive_hidden_states[torch.arange(len(embed_indices)), embed_indices - 1] # (batch_size, embed_dim)

            if inference:
                if ids is not None:
                    return embed_features, ids
                elif qids is not None or dids is not None:
                    return embed_features, qids, dids
                return embed_features

            if has_hard_negative:
                embed1, embed2, embed3 = embed_features[:contrastive_batch_size], embed_features[contrastive_batch_size:2*contrastive_batch_size], embed_features[2*contrastive_batch_size:]
            else:
                embed1, embed2 = embed_features[:contrastive_batch_size], embed_features[contrastive_batch_size:]

            loss_fct = nn.CrossEntropyLoss()
            sim = Similarity(temp=0.05)

            # add normalization
            embed1 = F.normalize(embed1, dim=-1)
            embed2 = F.normalize(embed2, dim=-1)

            cos_sim = sim(embed1.unsqueeze(1), embed2.unsqueeze(0))

            if has_hard_negative:
                embed3 = F.normalize(embed3, dim=-1)
                embed1_embed3_cos = sim(embed1.unsqueeze(1), embed3.unsqueeze(0))
                cos_sim = torch.cat([cos_sim, embed1_embed3_cos], 1)

            nce_labels = torch.arange(cos_sim.size(0)).long().to(cos_sim.device)
            contrastive_loss = loss_fct(cos_sim, nce_labels)

        # Combine losses
        if language_loss is not None and contrastive_loss is not None:
            language_loss *= self.config.language_loss_weight
            total_loss = language_loss + contrastive_loss
        elif language_loss is not None:
            total_loss = language_loss
        elif contrastive_loss is not None:
            total_loss = contrastive_loss
        else:
            total_loss = None

        return ExtraLossOutput(loss=total_loss, loss_gen=language_loss, loss_emb=contrastive_loss)